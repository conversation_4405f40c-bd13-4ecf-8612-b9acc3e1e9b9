{% extends "base.html" %}

{% block title %}Enchants - Uproar{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-magic text-accent me-2"></i>
                    Enchants Overview
                </h1>
            </div>
            <div>
                <select id="dateSelect" class="form-select form-select-sm" style="min-width: 150px;">
                    <option value="current" {% if selected_date == 'current' %}selected{% endif %}>Current</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Compact Enchant Coverage Summary -->
<div class="row mb-3">
    <div class="col-12">
        <div class="enchant-summary-compact p-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                
                
            </div>
            <div class="enchant-counters-row d-flex gap-2">
                <div class="enchant-counter-compact missing-back">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Back</div>
                </div>
                <div class="enchant-counter-compact missing-chest">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Chest</div>
                </div>
                <div class="enchant-counter-compact missing-wrist">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Wrist</div>
                </div>
                <div class="enchant-counter-compact missing-legs">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Legs</div>
                </div>
                <div class="enchant-counter-compact missing-feet">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Feet</div>
                </div>
                <div class="enchant-counter-compact missing-finger-1">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Ring 1</div>
                </div>
                <div class="enchant-counter-compact missing-finger-2">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Ring 2</div>
                </div>
                <div class="enchant-counter-compact missing-main-hand">
                    <div class="counter-value">0</div>
                    <div class="counter-percentage">0%</div>
                    <div class="counter-label">Weapon</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enchant Tier Legend -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-3">
                <h6 class="mb-2">
                    <i class="fas fa-info-circle me-2"></i>
                    Enchantment Tier Legend
                </h6>
                <div class="d-flex gap-4 flex-wrap">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-times-circle text-danger me-2"></i>
                        <span>No Enchantment</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-danger me-2"></i>
                        <span>Tier 1 Enchantment</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-warning me-2"></i>
                        <span>Tier 2 Enchantment</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <span>Tier 3 Enchantment</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enchants Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Enchants
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name" class="text-start">
                                    <i class="fas fa-user me-1"></i>Character
                                </th>
                                <th data-sort="back" class="text-center">
                                    Back
                                </th>
                                <th data-sort="chest" class="text-center">
                                    Chest
                                </th>
                                <th data-sort="wrist" class="text-center">
                                    Wrist
                                </th>
                                <th data-sort="legs" class="text-center">
                                    Legs
                                </th>
                                <th data-sort="feet" class="text-center">
                                    Feet
                                </th>
                                <th data-sort="finger-1" class="text-center">
                                    Ring 1
                                </th>
                                <th data-sort="finger-2" class="text-center">
                                    Ring 2
                                </th>
                                <th data-sort="main-hand" class="text-center">
                                    Weapon
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td class="text-start">
                                    <span class="fw-bold">{{ character.name }}</span>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Back %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Back %}
                                            {% if character.Back_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.Back_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.Back_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Chest %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Chest %}
                                            {% if character.Chest_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.Chest_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.Chest_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.wrist %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.wrist %}
                                            {% if character.wrist_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.wrist_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.wrist_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Legs %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Legs %}
                                            {% if character.Legs_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.Legs_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.Legs_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.feet %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.feet %}
                                            {% if character.feet_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.feet_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.feet_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.finger_1 %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.finger_1 %}
                                            {% if character.finger_1_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.finger_1_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.finger_1_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.finger_2 %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.finger_2 %}
                                            {% if character.finger_2_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.finger_2_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.finger_2_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Main_hand %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Main_hand %}
                                            {% if character.Main_hand_tier == 1 %}
                                                <i class="fas fa-check-circle text-danger"></i>
                                            {% elif character.Main_hand_tier == 2 %}
                                                <i class="fas fa-check-circle text-warning"></i>
                                            {% elif character.Main_hand_tier == 3 %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% else %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.table-modern');
    const headers = table.querySelectorAll('th[data-sort]');
    let currentSort = { column: null, asc: true };

    // Load historical dates
    fetch('/get_historical_enchant_dates')
        .then(response => response.json())
        .then(data => {
            const dateSelect = document.getElementById('dateSelect');
            data.dates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                // Format date for display (YYYY-MM-DD)
                const displayDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;
                option.textContent = displayDate;
                if (date === '{{ selected_date }}') {
                    option.selected = true;
                }
                dateSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading historical dates:', error));

    // Handle date selection change
    document.getElementById('dateSelect').addEventListener('change', function() {
        const selectedDate = this.value;
        window.location.href = `/enchants?date=${selectedDate}`;
    });

    function updateCounters() {
        const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
        const totalRaiders = rows.length;

        // Update total raiders count
        const totalRaidersElement = document.getElementById('total-raiders-count');
        if (totalRaidersElement) {
            totalRaidersElement.textContent = totalRaiders;
        }

        const enchantCounters = {
            'back': 0,
            'chest': 0,
            'wrist': 0,
            'legs': 0,
            'feet': 0,
            'finger-1': 0,
            'finger-2': 0,
            'main-hand': 0
        };

        rows.forEach(row => {
            // Check each enchant slot for missing enchants
            if (row.querySelector('td:nth-child(2) .no-enchant')) enchantCounters['back']++;
            if (row.querySelector('td:nth-child(3) .no-enchant')) enchantCounters['chest']++;
            if (row.querySelector('td:nth-child(4) .no-enchant')) enchantCounters['wrist']++;
            if (row.querySelector('td:nth-child(5) .no-enchant')) enchantCounters['legs']++;
            if (row.querySelector('td:nth-child(6) .no-enchant')) enchantCounters['feet']++;
            if (row.querySelector('td:nth-child(7) .no-enchant')) enchantCounters['finger-1']++;
            if (row.querySelector('td:nth-child(8) .no-enchant')) enchantCounters['finger-2']++;
            if (row.querySelector('td:nth-child(9) .no-enchant')) enchantCounters['main-hand']++;
        });

        // Update enchant counters with percentages
        Object.entries(enchantCounters).forEach(([slot, missingCount]) => {
            const counterElement = document.querySelector(`.missing-${slot}`);
            if (counterElement) {
                const counterValue = counterElement.querySelector('.counter-value');
                const counterPercentage = counterElement.querySelector('.counter-percentage');

                if (counterValue) {
                    counterValue.textContent = missingCount;
                }

                if (counterPercentage && totalRaiders > 0) {
                    const enchantedCount = totalRaiders - missingCount;
                    const percentage = Math.round((enchantedCount / totalRaiders) * 100);
                    counterPercentage.textContent = `${percentage}%`;
                }

                // Update styling based on count
                if (missingCount === 0) {
                    counterElement.classList.add('zero-count');
                    counterElement.classList.remove('non-zero-count');
                } else {
                    counterElement.classList.add('non-zero-count');
                    counterElement.classList.remove('zero-count');
                }
            }
        });
    }

    // Update counters initially
    updateCounters();

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update counters after filtering
            updateCounters();
        });
    }
});
</script>

<!-- Additional CSS for enchant-specific styling -->
<style>
/* Compact enchant summary */
.enchant-summary-compact {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

/* Single row of compact counters */
.enchant-counters-row {
    flex-wrap: nowrap;
    overflow-x: auto;
}

.enchant-counter-compact {
    flex: 1;
    min-width: 0;
    text-align: center;
    padding: 0.75rem 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.enchant-counter-compact .counter-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.enchant-counter-compact .counter-percentage {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.enchant-counter-compact .counter-label {
    font-size: 0.7rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Color coding based on enchant coverage */
.enchant-counter-compact.zero-count {
    border-color: var(--success-color);
    background: rgba(35, 134, 54, 0.15);
}

.enchant-counter-compact.zero-count .counter-percentage {
    color: var(--success-color);
}

.enchant-counter-compact.non-zero-count {
    border-color: var(--danger-color);
    background: rgba(218, 54, 51, 0.15);
}

.enchant-counter-compact.non-zero-count .counter-percentage {
    color: var(--danger-color);
}

/* Compact table with less padding */
.table-modern th {
    padding: 0.3rem 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.table-modern td {
    padding: 0.5rem 0.5rem;
    vertical-align: middle;
    font-size: 0.85rem;
}

/* Column width control */
.table-modern th:first-child,
.table-modern td:first-child {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

.table-modern th:not(:first-child),
.table-modern td:not(:first-child) {
    width: calc((100% - 200px) / 8);
    text-align: center;
}

.enchant-piece {
    display: flex;
    align-items: center;
    justify-content: center;
}

.enchant-piece i {
    font-size: 1rem;
}
</style>
{% endblock %}
