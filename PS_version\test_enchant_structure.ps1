# Test script to examine enchantment structure
Write-Host "Starting enchantment structure test..." -ForegroundColor Green

try {
    # Load existing export data to examine structure
    $exportPath = ".\csv_export.csv"
    if (Test-Path $exportPath) {
        Write-Host "Found export file, examining structure..." -ForegroundColor Yellow
        $data = Import-Csv $exportPath
        $firstChar = $data[0]

        Write-Host "Character: $($firstChar.Charname)" -ForegroundColor Cyan
        Write-Host "Helmet enchant: $($firstChar.helmet_enchant)" -ForegroundColor Cyan
        Write-Host "Back enchant: $($firstChar.back_enchant)" -ForegroundColor Cyan
        Write-Host "Chest enchant: $($firstChar.chest_enchant)" -ForegroundColor Cyan

        Write-Host "Export file structure confirmed" -ForegroundColor Green
    } else {
        Write-Host "No export file found" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
